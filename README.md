# Cleartrip AI Flight Booking Agent

An AI-powered flight booking assistant for Cleartrip UAE that allows users to search and book flights through natural language conversation.

## 🚀 Features

- **Natural Language Processing**: Search flights using conversational language
- **Real-time Flight Search**: Mock integration with Cleartrip API
- **Interactive Chat Interface**: User-friendly chat-based interaction
- **Smart Filtering**: Filter flights by time, price, stops, and airline
- **Responsive Design**: Works on desktop and mobile devices
- **Quick Actions**: Pre-defined buttons for common queries
- **Flight Comparison**: Compare multiple flight options
- **Booking Integration**: Direct booking links to Cleartrip

## 🛠 Technology Stack

### Backend
- **Node.js** + **Express.js** - Server framework
- **OpenAI API** - Natural language processing (with fallback mock)
- **Axios** - HTTP client for API calls
- **CORS** - Cross-origin resource sharing
- **dotenv** - Environment variable management

### Frontend
- **HTML5** - Structure
- **CSS3** - Styling with modern design
- **Vanilla JavaScript** - Interactive functionality
- **Fetch API** - HTTP requests

### Development Tools
- **Nodemon** - Development server with auto-reload
- **npm** - Package management

## 📁 Project Structure

```
cleartrip-ai-agent/
├── backend/
│   ├── server.js              # Main server file
│   ├── routes/
│   │   ├── chat.js           # Chat API endpoints
│   │   └── flights.js        # Flight API endpoints
│   └── services/
│       ├── ai.js             # AI service (OpenAI + fallback)
│       ├── cleartrip.js      # Cleartrip API service (mock)
│       └── utils.js          # Utility functions
├── frontend/
│   ├── index.html            # Main HTML file
│   ├── css/
│   │   └── style.css         # Styles
│   └── js/
│       ├── app.js            # Main application logic
│       ├── chat.js           # Chat functionality
│       └── flights.js        # Flight display and booking
├── package.json              # Dependencies and scripts
├── .env                      # Environment variables
└── README.md                 # This file
```

## 🚀 Quick Start

### Prerequisites
- Node.js (v14 or higher)
- npm (v6 or higher)
- Optional: OpenAI API key for enhanced AI features

### Installation

1. **Clone or navigate to the project directory**
   ```bash
   cd cleartrip-ai-agent
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Configure environment variables**
   ```bash
   # Edit .env file
   PORT=3000
   OPENAI_API_KEY=your_openai_key_here  # Optional
   CLEARTRIP_BASE_URL=https://me.cleartrip.ae
   NODE_ENV=development
   ```

4. **Start the development server**
   ```bash
   npm run dev
   ```

5. **Access the application**
   - Frontend: http://localhost:3000
   - API: http://localhost:3000/api

## 🎯 How to Use

### Basic Flight Search
1. Open http://localhost:3000 in your browser
2. Type natural language queries like:
   - "I want to fly from Delhi to Mumbai tomorrow"
   - "Show me morning flights from Bangalore to Chennai"
   - "Find non-stop flights from Delhi to Goa under 5000 rupees"

### Quick Actions
- Use the quick action buttons for common queries
- Popular routes, time preferences, and filters

### Flight Booking
- Click "Book Now" on any flight card
- You'll be redirected to the Cleartrip booking page (mock)

## 🔧 API Documentation

### Chat Endpoints

#### POST /api/chat/message
Process user messages and return AI responses with flight results.

**Request:**
```json
{
  "message": "I want to fly from Delhi to Mumbai tomorrow",
  "conversationHistory": []
}
```

**Response:**
```json
{
  "intent": "search",
  "parameters": {
    "from": "Delhi",
    "to": "Mumbai",
    "departDate": "29/06/2025",
    "adults": 1,
    "children": 0,
    "infants": 0,
    "class": "Economy"
  },
  "filters": {},
  "response": "Great! I found flights from Delhi to Mumbai...",
  "missingInfo": [],
  "flights": [...],
  "timestamp": "2025-06-28T08:06:29.831Z"
}
```

#### GET /api/chat/health
Health check for chat service.

### Flight Endpoints

#### POST /api/flights/search
Direct flight search with parameters.

**Request:**
```json
{
  "from": "Delhi",
  "to": "Mumbai",
  "departDate": "29/06/2025",
  "adults": 1,
  "children": 0,
  "infants": 0,
  "class": "Economy"
}
```

#### POST /api/flights/book
Initiate flight booking.

**Request:**
```json
{
  "flightId": "FL123456",
  "passengerDetails": {}
}
```

#### GET /api/flights/popular-routes
Get popular flight routes.

#### GET /api/flights/details/:flightId
Get detailed flight information.

#### GET /api/flights/health
Health check for flights service.

## 🧪 Testing

### Manual Testing
1. **Start the server**: `npm run dev`
2. **Test API endpoints**:
   ```bash
   # Test chat message
   curl -X POST http://localhost:3000/api/chat/message \
     -H "Content-Type: application/json" \
     -d '{"message": "I want to fly from Delhi to Mumbai tomorrow"}'
   
   # Test health endpoints
   curl http://localhost:3000/api/chat/health
   curl http://localhost:3000/api/flights/health
   
   # Test popular routes
   curl http://localhost:3000/api/flights/popular-routes
   ```

3. **Test frontend**: Open http://localhost:3000 and interact with the chat

### Test Scenarios
- ✅ Basic flight search with complete information
- ✅ Partial information handling (missing date/destination)
- ✅ Time preference filtering (morning/afternoon/evening)
- ✅ Flight type filtering (non-stop/direct)
- ✅ Popular routes display
- ✅ Error handling for invalid requests
- ✅ Responsive design on mobile devices

## 🎨 User Interface

### Chat Interface
- Clean, modern design with gradient background
- Message bubbles for user and AI responses
- Typing indicators during processing
- Quick action buttons for common queries
- Auto-scrolling chat history

### Flight Display
- Card-based layout for flight results
- Clear departure/arrival times and airports
- Price highlighting and currency formatting
- Airline logos and flight duration
- Direct booking buttons

### Responsive Design
- Mobile-first approach
- Flexible grid layouts
- Touch-friendly buttons
- Optimized for various screen sizes

## 🔄 System Flow Diagram

```mermaid
graph TD
    A[User Input] --> B[Frontend Chat Interface]
    B --> C[Validate Input]
    C --> D[Send to Backend API]
    D --> E[AI Service Processing]
    E --> F{Has OpenAI Key?}
    F -->|Yes| G[OpenAI API Call]
    F -->|No| H[Mock AI Processing]
    G --> I[Parse AI Response]
    H --> I
    I --> J{Search Intent?}
    J -->|Yes| K[Extract Flight Parameters]
    J -->|No| L[Return Clarification]
    K --> M[Cleartrip Service]
    M --> N[Generate Mock Flight Data]
    N --> O[Apply Filters]
    O --> P[Return Flight Results]
    P --> Q[Frontend Display]
    Q --> R[User Interaction]
    R --> S{Book Flight?}
    S -->|Yes| T[Open Booking URL]
    S -->|No| U[Continue Chat]
    L --> Q
    T --> V[External Cleartrip Site]
    U --> A
```

## 🏗 Implementation Details

### AI Service Architecture
The AI service uses a hybrid approach:

1. **Primary**: OpenAI GPT-4 for natural language understanding
2. **Fallback**: Pattern-matching algorithm for basic functionality
3. **Response Format**: Structured JSON with intent, parameters, and filters

### Flight Search Logic
```javascript
// Example of flight search flow
const searchParams = {
    from: "Delhi",
    to: "Mumbai",
    departDate: "29/06/2025",
    adults: 1,
    class: "Economy"
};

// Mock data generation with realistic variations
const flights = generateMockFlights(searchParams);
const filteredFlights = applyFilters(flights, userFilters);
const sortedFlights = sortByPrice(filteredFlights);
```

### Error Handling Strategy
- **Network Errors**: Graceful fallback with user-friendly messages
- **API Failures**: Automatic retry with exponential backoff
- **Invalid Input**: Real-time validation with helpful suggestions
- **Service Unavailable**: Mock data fallback for demonstration

### Security Considerations
- **API Keys**: Server-side only, never exposed to frontend
- **Input Validation**: Sanitization of user inputs
- **CORS**: Configured for secure cross-origin requests
- **Rate Limiting**: Implemented to prevent abuse

### Performance Optimizations
- **Lazy Loading**: Flight cards loaded progressively
- **Caching**: Conversation history management
- **Debouncing**: Input validation with delays
- **Compression**: Gzip compression for API responses

## 🚀 Deployment Options

### Local Development
```bash
npm run dev  # Development server with hot reload
```

### Production Deployment

#### Option 1: Heroku
```bash
# Install Heroku CLI
npm install -g heroku

# Login and create app
heroku login
heroku create cleartrip-ai-agent

# Set environment variables
heroku config:set OPENAI_API_KEY=your_key_here
heroku config:set NODE_ENV=production

# Deploy
git push heroku main
```

#### Option 2: Railway
```bash
# Install Railway CLI
npm install -g @railway/cli

# Login and deploy
railway login
railway init
railway up
```

#### Option 3: DigitalOcean App Platform
1. Connect GitHub repository
2. Configure build settings
3. Set environment variables
4. Deploy automatically

#### Option 4: Vercel (Full-stack)
```bash
# Install Vercel CLI
npm install -g vercel

# Deploy
vercel --prod
```

## 🔧 Configuration

### Environment Variables
```bash
# Required
PORT=3000                                    # Server port
NODE_ENV=development                         # Environment mode

# Optional (for enhanced features)
OPENAI_API_KEY=your_openai_key_here         # OpenAI API key
CLEARTRIP_BASE_URL=https://me.cleartrip.ae  # Cleartrip base URL

# Database (future enhancement)
MONGODB_URI=mongodb://localhost:27017/flights
```

### Package.json Scripts
```json
{
  "scripts": {
    "start": "node backend/server.js",        # Production start
    "dev": "nodemon backend/server.js",       # Development with auto-reload
    "test": "echo \"No tests yet\"",          # Test placeholder
    "build": "echo \"No build step required\"" # Build placeholder
  }
}
```

## 🐛 Troubleshooting

### Common Issues

#### Server Won't Start
```bash
# Check if port is in use
lsof -i :3000

# Kill process if needed
kill -9 <PID>

# Restart server
npm run dev
```

#### API Calls Failing
1. Check server is running on correct port
2. Verify CORS configuration
3. Check network connectivity
4. Review server logs for errors

#### Frontend Not Loading
1. Ensure static files are served correctly
2. Check browser console for JavaScript errors
3. Verify API endpoints are accessible
4. Clear browser cache

#### OpenAI API Issues
1. Verify API key is correct
2. Check API quota and billing
3. System falls back to mock AI automatically

### Debug Mode
```bash
# Enable debug logging
DEBUG=* npm run dev

# Check specific service logs
DEBUG=cleartrip:* npm run dev
```

## 📈 Future Enhancements

### Planned Features
- [ ] Real Cleartrip API integration
- [ ] User authentication and profiles
- [ ] Booking history and management
- [ ] Multi-city and round-trip searches
- [ ] Price alerts and notifications
- [ ] Advanced filtering options
- [ ] Payment integration
- [ ] Mobile app development

### Technical Improvements
- [ ] Unit and integration tests
- [ ] Database integration (MongoDB)
- [ ] Caching layer (Redis)
- [ ] API rate limiting
- [ ] Logging and monitoring
- [ ] CI/CD pipeline
- [ ] Docker containerization
- [ ] Kubernetes deployment

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the ISC License.

## 📞 Support

For support and questions:
- Create an issue in the repository
- Check the troubleshooting section
- Review the API documentation

---

**Built with ❤️ for Cleartrip UAE**
