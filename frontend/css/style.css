* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', Aria<PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    line-height: 1.6;
}

.container {
    max-width: 900px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

header {
    text-align: center;
    color: white;
    margin-bottom: 30px;
    padding: 20px 0;
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

header p {
    font-size: 1.2rem;
    opacity: 0.9;
}

.chat-container {
    background: white;
    border-radius: 20px;
    padding: 25px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    margin-bottom: 25px;
    flex: 1;
}

.chat-messages {
    height: 400px;
    overflow-y: auto;
    margin-bottom: 20px;
    padding: 15px;
    border: 1px solid #f0f0f0;
    border-radius: 15px;
    background: #fafafa;
}

.chat-messages::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

.message {
    margin-bottom: 20px;
    display: flex;
    align-items: flex-start;
    animation: fadeIn 0.3s ease-in;
}

.message.user {
    justify-content: flex-end;
}

.message-content {
    max-width: 75%;
    padding: 15px 20px;
    border-radius: 20px;
    line-height: 1.5;
    word-wrap: break-word;
}

.message.user .message-content {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border-bottom-right-radius: 5px;
}

.message.assistant .message-content {
    background: white;
    color: #333;
    border: 1px solid #e9ecef;
    border-bottom-left-radius: 5px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.message.assistant .message-content ul {
    margin: 10px 0;
    padding-left: 20px;
}

.message.assistant .message-content li {
    margin: 5px 0;
}

.quick-actions {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    flex-wrap: wrap;
    justify-content: center;
}

.quick-btn {
    padding: 10px 18px;
    border: 2px solid #007bff;
    background: white;
    color: #007bff;
    border-radius: 25px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.quick-btn:hover {
    background: #007bff;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,123,255,0.3);
}

.input-container {
    display: flex;
    gap: 12px;
    align-items: center;
}

#user-input {
    flex: 1;
    padding: 15px 20px;
    border: 2px solid #e9ecef;
    border-radius: 30px;
    font-size: 16px;
    outline: none;
    transition: border-color 0.3s ease;
}

#user-input:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
}

#send-btn {
    padding: 15px 25px;
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border: none;
    border-radius: 30px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 600;
    transition: all 0.3s ease;
    min-width: 80px;
}

#send-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0,123,255,0.4);
}

#send-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.flights-container {
    display: grid;
    gap: 20px;
    margin-bottom: 30px;
}

.flight-card {
    background: white;
    border-radius: 20px;
    padding: 25px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border: 1px solid #f0f0f0;
}

.flight-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.15);
}

.flight-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #f0f0f0;
}

.airline {
    font-weight: 700;
    color: #333;
    font-size: 1.1rem;
}

.price {
    font-size: 1.8rem;
    font-weight: 700;
    color: #007bff;
}

.flight-details {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 25px;
    align-items: center;
    margin-bottom: 20px;
}

.flight-time {
    text-align: center;
}

.time {
    font-size: 1.4rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 5px;
}

.airport {
    color: #666;
    font-size: 14px;
    font-weight: 500;
}

.flight-duration {
    text-align: center;
    color: #666;
    font-size: 14px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 10px;
}

.flight-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    font-size: 14px;
    color: #666;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 10px;
}

.book-btn {
    width: 100%;
    padding: 15px;
    background: linear-gradient(135deg, #ff6b35, #e55a2b);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
}

.book-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255,107,53,0.4);
}

.loading-spinner {
    display: inline-block;
    animation: spin 1s linear infinite;
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading-content {
    background: white;
    padding: 40px;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 20px 60px rgba(0,0,0,0.3);
}

.loading-spinner-large {
    font-size: 3rem;
    animation: bounce 1s infinite;
    margin-bottom: 20px;
}

.error-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
}

.error-message {
    background: #dc3545;
    color: white;
    padding: 15px 20px;
    border-radius: 10px;
    box-shadow: 0 4px 12px rgba(220,53,69,0.3);
    display: flex;
    align-items: center;
    gap: 10px;
    max-width: 400px;
}

.error-close {
    background: none;
    border: none;
    color: white;
    font-size: 20px;
    cursor: pointer;
    padding: 0;
    margin-left: auto;
}

.typing-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #666;
    font-style: italic;
}

.typing-dots {
    display: flex;
    gap: 4px;
}

.typing-dots span {
    width: 8px;
    height: 8px;
    background: #666;
    border-radius: 50%;
    animation: typing 1.4s infinite;
}

.typing-dots span:nth-child(2) { animation-delay: 0.2s; }
.typing-dots span:nth-child(3) { animation-delay: 0.4s; }

footer {
    text-align: center;
    color: white;
    opacity: 0.8;
    padding: 20px 0;
    margin-top: auto;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

@keyframes typing {
    0%, 60%, 100% { opacity: 0.3; transform: scale(0.8); }
    30% { opacity: 1; transform: scale(1); }
}

@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    .chat-container {
        padding: 20px;
    }
    
    .chat-messages {
        height: 300px;
    }
    
    .flight-details {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 15px;
    }
    
    .quick-actions {
        justify-content: center;
    }
    
    .quick-btn {
        font-size: 12px;
        padding: 8px 15px;
    }
    
    .input-container {
        flex-direction: column;
        gap: 10px;
    }
    
    #user-input {
        width: 100%;
    }
    
    #send-btn {
        width: 100%;
    }
}

/* Compact Flight Cards in Chat */
.flight-results-message {
    background: transparent !important;
    padding: 0 !important;
    margin: 15px 0 !important;
}

.compact-flights-container {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 20px;
    border: 1px solid #e9ecef;
}

.compact-flights-header {
    margin-bottom: 15px;
}

.flight-search-info {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.route {
    font-weight: 600;
    color: #2c3e50;
    font-size: 1.1rem;
}

.route-detail {
    color: #6c757d;
    font-size: 0.9rem;
}

.date-detail {
    color: #6c757d;
    font-size: 0.85rem;
}

.compact-flights-grid {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.compact-flight-card {
    background: white;
    border-radius: 12px;
    padding: 16px;
    border: 1px solid #e9ecef;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.compact-flight-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    border-color: #667eea;
}

.compact-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.airline-code {
    font-weight: 600;
    color: #2c3e50;
    font-size: 1.1rem;
}

.price {
    font-weight: 700;
    color: #007bff;
    font-size: 1.2rem;
}

.compact-flight-route {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
}

.departure, .arrival {
    text-align: center;
    flex: 1;
}

.departure .time, .arrival .time {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 2px;
}

.departure .airport, .arrival .airport {
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 2px;
}

.departure .city, .arrival .city {
    font-size: 0.8rem;
    color: #adb5bd;
}

.flight-info {
    text-align: center;
    flex: 1;
    padding: 0 15px;
}

.duration {
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 4px;
}

.stops {
    font-size: 0.8rem;
    color: #6c757d;
}

.compact-card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.85rem;
    color: #6c757d;
}

.fare-type {
    display: flex;
    align-items: center;
    gap: 4px;
}

.direct-badge {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 2px 8px;
    background: #e8f5e8;
    color: #28a745;
    border-radius: 12px;
    font-size: 0.8rem;
}

.processing-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 12px;
    font-weight: 600;
    color: #667eea;
}

/* Mobile responsiveness for compact cards */
@media (max-width: 768px) {
    .compact-flight-route {
        flex-direction: column;
        gap: 10px;
    }

    .flight-info {
        padding: 10px 0;
        border-top: 1px solid #e9ecef;
        border-bottom: 1px solid #e9ecef;
    }

    .compact-card-footer {
        flex-direction: column;
        gap: 8px;
        align-items: flex-start;
    }
}
