class FlightAssistant {
    constructor() {
        this.conversationHistory = [];
        this.apiBase = window.location.origin + '/api';
        this.isProcessing = false;
        this.initializeEventListeners();
        this.initializeApp();
    }

    initializeApp() {
        console.log('🚀 Cleartrip AI Flight Assistant initialized');
        this.showWelcomeMessage();
    }

    showWelcomeMessage() {
        // Welcome message is already in HTML, no need to add programmatically
    }

    initializeEventListeners() {
        const userInput = document.getElementById('user-input');
        const sendBtn = document.getElementById('send-btn');

        // Enter key to send message
        userInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });

        // Send button click
        sendBtn.addEventListener('click', () => {
            this.sendMessage();
        });

        // Auto-focus on input
        userInput.focus();
    }

    async sendMessage() {
        const input = document.getElementById('user-input');
        const message = input.value.trim();
        
        if (!message || this.isProcessing) return;

        // Disable input during processing
        this.setProcessingState(true);

        // Add user message to chat
        this.addMessage(message, 'user');
        input.value = '';

        // Show typing indicator
        this.showTypingIndicator();

        try {
            const response = await fetch(`${this.apiBase}/chat/message`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    message,
                    conversationHistory: this.conversationHistory
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            
            // Remove typing indicator
            this.hideTypingIndicator();

            // Add AI response to chat
            this.addMessage(data.response, 'assistant');

            // Display flights if any
            if (data.flights && data.flights.length > 0) {
                this.displayFlightsInChat(data.flights, data.searchParams);
            } else if (data.intent === 'search' && data.missingInfo.length === 0) {
                // If search was attempted but no flights found
                this.addMessage('Sorry, no flights were found for your search criteria. Please try different dates or destinations.', 'assistant');
            }

            // Update conversation history
            this.conversationHistory.push(
                { role: 'user', content: message },
                { role: 'assistant', content: data.response }
            );

            // Keep conversation history manageable
            if (this.conversationHistory.length > 20) {
                this.conversationHistory = this.conversationHistory.slice(-20);
            }

        } catch (error) {
            this.hideTypingIndicator();
            console.error('Error:', error);
            
            let errorMessage = 'Sorry, I encountered an error. Please try again.';
            if (error.message.includes('Failed to fetch')) {
                errorMessage = 'Unable to connect to the server. Please check your internet connection and try again.';
            }
            
            this.addMessage(errorMessage, 'assistant');
            this.showError(error.message);
        } finally {
            this.setProcessingState(false);
        }
    }

    setProcessingState(processing) {
        this.isProcessing = processing;
        const sendBtn = document.getElementById('send-btn');
        const sendText = document.getElementById('send-text');
        const sendLoading = document.getElementById('send-loading');
        const userInput = document.getElementById('user-input');

        if (processing) {
            sendBtn.disabled = true;
            sendText.style.display = 'none';
            sendLoading.style.display = 'inline';
            userInput.disabled = true;
        } else {
            sendBtn.disabled = false;
            sendText.style.display = 'inline';
            sendLoading.style.display = 'none';
            userInput.disabled = false;
            userInput.focus();
        }
    }

    addMessage(content, sender) {
        const messagesContainer = document.getElementById('chat-messages');
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}`;
        
        messageDiv.innerHTML = `
            <div class="message-content">${this.formatMessage(content)}</div>
        `;
        
        messagesContainer.appendChild(messageDiv);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }

    formatMessage(content) {
        // Convert line breaks to HTML
        return content.replace(/\n/g, '<br>');
    }

    showTypingIndicator() {
        const messagesContainer = document.getElementById('chat-messages');
        const typingDiv = document.createElement('div');
        typingDiv.className = 'message assistant';
        typingDiv.id = 'typing-indicator';
        
        typingDiv.innerHTML = `
            <div class="message-content typing-indicator">
                AI is thinking
                <div class="typing-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        `;
        
        messagesContainer.appendChild(typingDiv);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }

    hideTypingIndicator() {
        const typingIndicator = document.getElementById('typing-indicator');
        if (typingIndicator) {
            typingIndicator.remove();
        }
    }

    displayFlightsInChat(flights, searchParams) {
        if (flights.length === 0) {
            this.addMessage('No flights found for your search criteria.', 'assistant');
            return;
        }

        // Create compact flight cards HTML
        const flightCardsHtml = this.createCompactFlightCards(flights, searchParams);

        // Add as a special message type
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message assistant-message flight-results-message';
        messageDiv.innerHTML = flightCardsHtml;

        const chatContainer = document.getElementById('chat-container');
        chatContainer.appendChild(messageDiv);
        chatContainer.scrollTop = chatContainer.scrollHeight;
    }

    displayFlights(flights) {
        const container = document.getElementById('flights-container');
        container.innerHTML = '';

        if (flights.length === 0) {
            container.innerHTML = `
                <div class="no-flights">
                    <h3>No flights found</h3>
                    <p>Try adjusting your search criteria or dates.</p>
                </div>
            `;
            return;
        }

        const flightsHeader = document.createElement('div');
        flightsHeader.className = 'flights-header';
        flightsHeader.innerHTML = `
            <h3>✈️ Found ${flights.length} flight${flights.length > 1 ? 's' : ''} for you:</h3>
        `;
        container.appendChild(flightsHeader);

        flights.forEach((flight, index) => {
            const flightCard = this.createFlightCard(flight, index);
            container.appendChild(flightCard);
        });
    }

    createFlightCard(flight) {
        const card = document.createElement('div');
        card.className = 'flight-card';
        
        card.innerHTML = `
            <div class="flight-header">
                <div class="airline">${flight.airline}</div>
                <div class="price">${flight.currency} ${flight.price.toLocaleString()}</div>
            </div>
            
            <div class="flight-details">
                <div class="flight-time">
                    <div class="time">${flight.departure.time}</div>
                    <div class="airport">${flight.departure.airport}</div>
                    <div class="city">${flight.departure.city || ''}</div>
                </div>
                
                <div class="flight-duration">
                    <div>✈️ ${flight.duration}</div>
                    <div>${flight.stops === 0 ? 'Non-stop' : flight.stops + ' stop(s)'}</div>
                </div>
                
                <div class="flight-time">
                    <div class="time">${flight.arrival.time}</div>
                    <div class="airport">${flight.arrival.airport}</div>
                    <div class="city">${flight.arrival.city || ''}</div>
                </div>
            </div>
            
            <div class="flight-info">
                <span>📋 ${flight.fareType}</span>
                <span>${flight.stops === 0 ? '🎯 Direct' : '🔄 ' + flight.stops + ' Stop(s)'}</span>
            </div>
            
            <button class="book-btn" onclick="bookFlight('${flight.id}', '${flight.airline}', '${flight.price}')">
                Book Now - ${flight.currency} ${flight.price.toLocaleString()}
            </button>
        `;
        
        return card;
    }

    createCompactFlightCards(flights, searchParams) {
        const route = searchParams ? `${searchParams.from} → ${searchParams.to}` : '';
        const date = searchParams ? searchParams.departDate : '';

        const header = `
            <div class="compact-flights-header">
                <div class="flight-search-info">
                    <span class="route">✈️ Found ${flights.length} flight${flights.length > 1 ? 's' : ''} for you:</span>
                    ${route ? `<span class="route-detail">${route}</span>` : ''}
                    ${date ? `<span class="date-detail">${date}</span>` : ''}
                </div>
            </div>
        `;

        const cardsHtml = flights.map(flight => this.createCompactFlightCard(flight)).join('');

        return `
            <div class="compact-flights-container">
                ${header}
                <div class="compact-flights-grid">
                    ${cardsHtml}
                </div>
            </div>
        `;
    }

    createCompactFlightCard(flight) {
        const priceDisplay = flight.price > 0 ? `${flight.currency} ${flight.price.toLocaleString()}` : 'AED 0';

        return `
            <div class="compact-flight-card" onclick="selectFlight('${flight.id}')">
                <div class="compact-card-header">
                    <span class="airline-code">${flight.airline}</span>
                    <span class="price">${priceDisplay}</span>
                </div>

                <div class="compact-flight-route">
                    <div class="departure">
                        <div class="time">${flight.departure.time}</div>
                        <div class="airport">${flight.departure.airport}</div>
                        <div class="city">${flight.departure.city}</div>
                    </div>

                    <div class="flight-info">
                        <div class="duration">✈️ ${flight.duration}</div>
                        <div class="stops">${flight.stops === 0 ? 'Non-stop' : flight.stops + ' stop(s)'}</div>
                    </div>

                    <div class="arrival">
                        <div class="time">${flight.arrival.time}</div>
                        <div class="airport">${flight.arrival.airport}</div>
                        <div class="city">${flight.arrival.city}</div>
                    </div>
                </div>

                <div class="compact-card-footer">
                    <span class="fare-type">📋 ${flight.fareType}</span>
                    <span class="direct-badge">${flight.stops === 0 ? '🎯 Direct' : '🔄 ' + flight.stops + ' Stop(s)'}</span>
                </div>

                <div class="processing-overlay" style="display: none;">
                    <span>🔄 Processing...</span>
                </div>
            </div>
        `;
    }

    scrollToFlights() {
        setTimeout(() => {
            const flightsContainer = document.getElementById('flights-container');
            if (flightsContainer) {
                flightsContainer.scrollIntoView({ 
                    behavior: 'smooth', 
                    block: 'start' 
                });
            }
        }, 300);
    }

    showError(message) {
        const errorContainer = document.getElementById('error-container');
        const errorText = document.getElementById('error-text');
        
        errorText.textContent = message;
        errorContainer.style.display = 'block';
        
        // Auto-hide after 5 seconds
        setTimeout(() => {
            this.hideError();
        }, 5000);
    }

    hideError() {
        const errorContainer = document.getElementById('error-container');
        errorContainer.style.display = 'none';
    }
}

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.flightAssistant = new FlightAssistant();
});

// Global functions for HTML onclick handlers
function sendQuickMessage(message) {
    const input = document.getElementById('user-input');
    input.value = message;
    if (window.flightAssistant) {
        window.flightAssistant.sendMessage();
    }
}

function hideError() {
    if (window.flightAssistant) {
        window.flightAssistant.hideError();
    }
}

function selectFlight(flightId) {
    const card = event.currentTarget;
    const overlay = card.querySelector('.processing-overlay');

    // Show processing state
    overlay.style.display = 'flex';

    // Simulate booking process
    setTimeout(() => {
        overlay.style.display = 'none';

        // Add booking confirmation message
        if (window.flightAssistant) {
            window.flightAssistant.addMessage(`✅ Flight ${flightId} selected! You can now proceed with booking details.`, 'assistant');
        }
    }, 1500);
}
