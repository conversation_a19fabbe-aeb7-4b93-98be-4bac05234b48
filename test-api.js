#!/usr/bin/env node

/**
 * Simple test script for Cleartrip AI Agent API
 * Run with: node test-api.js
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api';

// Test scenarios
const testScenarios = [
    {
        name: 'Complete flight search',
        endpoint: '/chat/message',
        method: 'POST',
        data: {
            message: 'I want to fly from Delhi to Mumbai tomorrow',
            conversationHistory: []
        }
    },
    {
        name: 'Partial information search',
        endpoint: '/chat/message',
        method: 'POST',
        data: {
            message: 'Show me flights from Bangalore to Chennai',
            conversationHistory: []
        }
    },
    {
        name: 'Morning flights filter',
        endpoint: '/chat/message',
        method: 'POST',
        data: {
            message: 'I need morning flights from Delhi to Goa tomorrow',
            conversationHistory: []
        }
    },
    {
        name: 'Non-stop flights filter',
        endpoint: '/chat/message',
        method: 'POST',
        data: {
            message: 'Find non-stop flights from Mumbai to Pune',
            conversationHistory: []
        }
    },
    {
        name: 'Popular routes',
        endpoint: '/flights/popular-routes',
        method: 'GET'
    },
    {
        name: 'Chat health check',
        endpoint: '/chat/health',
        method: 'GET'
    },
    {
        name: 'Flights health check',
        endpoint: '/flights/health',
        method: 'GET'
    },
    {
        name: 'Direct flight search',
        endpoint: '/flights/search',
        method: 'POST',
        data: {
            from: 'Delhi',
            to: 'Mumbai',
            departDate: '30/06/2025',
            adults: 1,
            children: 0,
            infants: 0,
            class: 'Economy'
        }
    }
];

async function runTest(scenario) {
    try {
        console.log(`\n🧪 Testing: ${scenario.name}`);
        console.log(`📡 ${scenario.method} ${scenario.endpoint}`);
        
        const config = {
            method: scenario.method,
            url: `${BASE_URL}${scenario.endpoint}`,
            headers: {
                'Content-Type': 'application/json'
            }
        };
        
        if (scenario.data) {
            config.data = scenario.data;
            console.log(`📤 Request:`, JSON.stringify(scenario.data, null, 2));
        }
        
        const response = await axios(config);
        
        console.log(`✅ Status: ${response.status}`);
        console.log(`📥 Response:`, JSON.stringify(response.data, null, 2));
        
        // Validate response structure
        if (scenario.endpoint.includes('/chat/message')) {
            const { intent, parameters, response: aiResponse, flights } = response.data;
            console.log(`🤖 AI Intent: ${intent}`);
            console.log(`📋 Parameters: ${JSON.stringify(parameters)}`);
            if (flights && flights.length > 0) {
                console.log(`✈️ Found ${flights.length} flights`);
            }
        }
        
        return { success: true, scenario: scenario.name };
        
    } catch (error) {
        console.log(`❌ Error: ${error.message}`);
        if (error.response) {
            console.log(`📥 Error Response:`, error.response.data);
        }
        return { success: false, scenario: scenario.name, error: error.message };
    }
}

async function runAllTests() {
    console.log('🚀 Starting Cleartrip AI Agent API Tests');
    console.log('=' .repeat(50));
    
    const results = [];
    
    for (const scenario of testScenarios) {
        const result = await runTest(scenario);
        results.push(result);
        
        // Wait a bit between tests
        await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    // Summary
    console.log('\n' + '=' .repeat(50));
    console.log('📊 Test Results Summary');
    console.log('=' .repeat(50));
    
    const passed = results.filter(r => r.success).length;
    const failed = results.filter(r => !r.success).length;
    
    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`📈 Success Rate: ${((passed / results.length) * 100).toFixed(1)}%`);
    
    if (failed > 0) {
        console.log('\n❌ Failed Tests:');
        results.filter(r => !r.success).forEach(r => {
            console.log(`  - ${r.scenario}: ${r.error}`);
        });
    }
    
    console.log('\n🎉 Testing completed!');
    
    if (failed === 0) {
        console.log('🌟 All tests passed! The API is working correctly.');
    } else {
        console.log('⚠️  Some tests failed. Please check the server and try again.');
        process.exit(1);
    }
}

// Check if server is running
async function checkServer() {
    try {
        await axios.get(`${BASE_URL}/chat/health`);
        console.log('✅ Server is running');
        return true;
    } catch (error) {
        console.log('❌ Server is not running. Please start it with: npm run dev');
        return false;
    }
}

// Main execution
async function main() {
    const serverRunning = await checkServer();
    if (serverRunning) {
        await runAllTests();
    }
}

main().catch(console.error);
