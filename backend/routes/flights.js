const express = require('express');
const router = express.Router();
const cleartripService = require('../services/cleartrip');

// Search flights endpoint
router.post('/search', async (req, res) => {
    try {
        const { from, to, departDate, adults, children, infants, class: flightClass } = req.body;
        
        // Validate required parameters
        if (!from || !to || !departDate) {
            return res.status(400).json({ 
                error: 'Missing required parameters: from, to, and departDate are required' 
            });
        }
        
        const searchParams = {
            from,
            to,
            departDate,
            adults: adults || 1,
            children: children || 0,
            infants: infants || 0,
            class: flightClass || 'Economy'
        };
        
        console.log('Direct flight search with params:', searchParams);
        
        const flights = await cleartripService.searchFlights(searchParams);
        
        res.json({ 
            flights,
            searchParams,
            count: flights.length,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        console.error('Flight search error:', error);
        res.status(500).json({ 
            error: 'Flight search failed',
            details: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
});

// Book flight endpoint
router.post('/book', async (req, res) => {
    try {
        const { flightId, passengerDetails } = req.body;
        
        if (!flightId) {
            return res.status(400).json({ error: 'Flight ID is required' });
        }
        
        console.log('Booking flight:', flightId);
        
        // For demo purposes, create a mock booking
        const bookingResult = await cleartripService.createBookingItinerary({
            flightId,
            passengerDetails
        });

        console.log('📋 [FLIGHTS ROUTE] Booking result received:', JSON.stringify(bookingResult, null, 2));
        console.log('🔍 [FLIGHTS ROUTE] Booking result type:', typeof bookingResult);
        console.log('🔍 [FLIGHTS ROUTE] Has bookingUrl:', !!bookingResult?.bookingUrl);

        res.json({
            success: true,
            bookingUrl: bookingResult.bookingUrl,
            bookingId: bookingResult.bookingId,
            message: 'Booking initiated successfully. You will be redirected to complete the payment.',
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        console.error('Flight booking error:', error);
        res.status(500).json({ 
            error: 'Flight booking failed',
            details: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
});

// Get flight details by ID
router.get('/details/:flightId', async (req, res) => {
    try {
        const { flightId } = req.params;
        
        // For demo purposes, return mock flight details
        const flightDetails = {
            id: flightId,
            airline: 'IndiGo',
            flightNumber: '6E-123',
            aircraft: 'Airbus A320',
            departure: {
                time: '10:30',
                airport: 'DEL',
                city: 'Delhi',
                terminal: 'T3'
            },
            arrival: {
                time: '13:00',
                airport: 'BOM',
                city: 'Mumbai',
                terminal: 'T2'
            },
            duration: '2h 30m',
            price: 4500,
            currency: 'INR',
            stops: 0,
            fareType: 'Economy',
            baggage: {
                checkedIn: '15 kg',
                cabinBag: '7 kg'
            },
            amenities: ['Meals', 'Entertainment', 'WiFi'],
            cancellationPolicy: 'Cancellation fee applies',
            timestamp: new Date().toISOString()
        };
        
        res.json(flightDetails);
    } catch (error) {
        console.error('Flight details error:', error);
        res.status(500).json({ 
            error: 'Failed to fetch flight details',
            details: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
});

// Get popular routes
router.get('/popular-routes', (req, res) => {
    try {
        const popularRoutes = [
            { from: 'Delhi', to: 'Mumbai', fromCode: 'DEL', toCode: 'BOM' },
            { from: 'Delhi', to: 'Bangalore', fromCode: 'DEL', toCode: 'BLR' },
            { from: 'Mumbai', to: 'Chennai', fromCode: 'BOM', toCode: 'MAA' },
            { from: 'Delhi', to: 'Chennai', fromCode: 'DEL', toCode: 'MAA' },
            { from: 'Mumbai', to: 'Kolkata', fromCode: 'BOM', toCode: 'CCU' },
            { from: 'Bangalore', to: 'Hyderabad', fromCode: 'BLR', toCode: 'HYD' },
            { from: 'Delhi', to: 'Goa', fromCode: 'DEL', toCode: 'GOI' },
            { from: 'Mumbai', to: 'Pune', fromCode: 'BOM', toCode: 'PNQ' }
        ];
        
        res.json({
            routes: popularRoutes,
            count: popularRoutes.length,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        console.error('Popular routes error:', error);
        res.status(500).json({ 
            error: 'Failed to fetch popular routes',
            details: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
});

// Health check endpoint
router.get('/health', (req, res) => {
    res.json({ 
        status: 'ok', 
        service: 'flights',
        timestamp: new Date().toISOString()
    });
});

module.exports = router;
