const OpenAI = require('openai');

class AIService {
    constructor() {
        this.openai = new OpenAI({
            apiKey: process.env.OPENAI_API_KEY
        });

        this.systemPrompt = `
You are a flight booking assistant for Cleartrip UAE. Help users search and book flights through natural conversation.

Extract flight search parameters from user messages:
- Origin and destination cities/airports
- Departure date (and return date if round trip)
- Number of passengers (adults, children, infants)
- Class preference (Economy, Business, First)
- Filters: time preference (morning, afternoon, evening), stops (nonstop, onestop), airline, price range

Always respond with JSON in this format:
{
    "intent": "search|filter|book|clarify",
    "parameters": {
        "from": "city_name",
        "to": "city_name", 
        "departDate": "DD/MM/YYYY",
        "adults": 1,
        "children": 0,
        "infants": 0,
        "class": "Economy"
    },
    "filters": {
        "timePreference": "morning|afternoon|evening",
        "stops": "nonstop|onestop|any",
        "airline": "airline_name",
        "priceRange": [min, max]
    },
    "response": "Natural language response to user",
    "missingInfo": ["list of missing required parameters"]
}

Popular cities: Delhi, Mumbai, Bangalore, Chennai, Kolkata, Hyderabad, Pune, Ahmedabad, Goa, Kochi

Current date context: ${new Date().toLocaleDateString('en-GB')}

Be helpful, friendly, and ask for clarification when needed. If the user provides incomplete information, ask for the missing details in a conversational way.
`;
    }

    async processMessage(message, conversationHistory = []) {
        try {
            // Check if OpenAI API key is configured
            if (!process.env.OPENAI_API_KEY || process.env.OPENAI_API_KEY === 'your_openai_key_here') {
                return this.getMockAIResponse(message);
            }

            const messages = [
                { role: 'system', content: this.systemPrompt },
                ...conversationHistory.slice(-10), // Keep last 10 messages for context
                { role: 'user', content: message }
            ];

            const response = await this.openai.chat.completions.create({
                model: 'gpt-4',
                messages,
                temperature: 0.3,
                response_format: { type: "json_object" }
            });

            const aiResponse = JSON.parse(response.choices[0].message.content);
            return this.validateAndCleanResponse(aiResponse);
        } catch (error) {
            console.error('AI processing error:', error.message);
            
            // Fallback to mock response if AI fails
            return this.getMockAIResponse(message);
        }
    }

    getMockAIResponse(message) {
        const lowerMessage = message.toLowerCase();
        
        // Simple pattern matching for demo purposes
        const patterns = {
            search: /(?:fly|flight|book|search|travel|go).+(?:from|to)/i,
            from: /(?:from|leaving|departing)\s+([a-z\s]+?)(?:\s+to|\s+going|\s+and|$)/i,
            to: /(?:to|going\s+to|arriving\s+in)\s+([a-z\s]+?)(?:\s+on|\s+tomorrow|\s+today|\s+and|$)/i,
            date: /(?:tomorrow|today|(\d{1,2})[\/\-](\d{1,2})[\/\-](\d{2,4})|(\d{1,2})\s+(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec))/i,
            morning: /morning|early|6am|7am|8am|9am|10am/i,
            afternoon: /afternoon|noon|12pm|1pm|2pm|3pm|4pm/i,
            evening: /evening|night|5pm|6pm|7pm|8pm|9pm|10pm/i
        };

        let intent = 'clarify';
        let parameters = {
            adults: 1,
            children: 0,
            infants: 0,
            class: 'Economy'
        };
        let filters = {};
        let missingInfo = [];
        let response = "I'd be happy to help you find flights! Could you please tell me where you'd like to fly from and to, and when you'd like to travel?";

        // Extract origin and destination using a more specific approach
        const flightRouteMatch = lowerMessage.match(/(?:from|leaving)\s+([a-z\s]+?)\s+(?:to|going\s+to)\s+([a-z\s]+?)(?:\s+on|\s+tomorrow|\s+today|$)/i);

        if (flightRouteMatch) {
            parameters.from = this.cleanCityName(flightRouteMatch[1]);
            parameters.to = this.cleanCityName(flightRouteMatch[2]);
        } else {
            // Fallback to individual extraction
            const fromMatch = lowerMessage.match(/(?:from|leaving)\s+([a-z\s]+?)(?:\s+to|\s+going|\s+and|$)/i);
            if (fromMatch) {
                parameters.from = this.cleanCityName(fromMatch[1]);
            }

            const toMatch = lowerMessage.match(/(?:to|going\s+to|arriving\s+in)\s+([a-z\s]+?)(?:\s+on|\s+tomorrow|\s+today|\s+and|$)/i);
            if (toMatch) {
                parameters.to = this.cleanCityName(toMatch[1]);
            }
        }

        // Extract date
        if (lowerMessage.includes('tomorrow')) {
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            parameters.departDate = tomorrow.toLocaleDateString('en-GB');
        } else if (lowerMessage.includes('today')) {
            parameters.departDate = new Date().toLocaleDateString('en-GB');
        }

        // Extract time preference
        if (patterns.morning.test(lowerMessage)) {
            filters.timePreference = 'morning';
        } else if (patterns.afternoon.test(lowerMessage)) {
            filters.timePreference = 'afternoon';
        } else if (patterns.evening.test(lowerMessage)) {
            filters.timePreference = 'evening';
        }

        // Check if we have enough info for search
        if (parameters.from && parameters.to && parameters.departDate) {
            intent = 'search';
            response = `Great! I found flights from ${parameters.from} to ${parameters.to} on ${parameters.departDate}. Let me search for the best options for you.`;
        } else {
            // Determine what's missing
            if (!parameters.from) missingInfo.push('departure city');
            if (!parameters.to) missingInfo.push('destination city');
            if (!parameters.departDate) missingInfo.push('travel date');

            if (missingInfo.length > 0) {
                response = `I need a bit more information. Could you please tell me your ${missingInfo.join(' and ')}?`;
            }
        }

        return {
            intent,
            parameters,
            filters,
            response,
            missingInfo
        };
    }

    cleanCityName(cityName) {
        return cityName.trim()
            .split(' ')
            .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
            .join(' ');
    }

    validateAndCleanResponse(response) {
        // Ensure required fields exist
        const defaultResponse = {
            intent: 'clarify',
            parameters: {
                adults: 1,
                children: 0,
                infants: 0,
                class: 'Economy'
            },
            filters: {},
            response: "I'd be happy to help you find flights!",
            missingInfo: []
        };

        return {
            ...defaultResponse,
            ...response,
            parameters: {
                ...defaultResponse.parameters,
                ...response.parameters
            },
            filters: {
                ...defaultResponse.filters,
                ...response.filters
            }
        };
    }
}

module.exports = new AIService();
