const { GoogleGenerativeAI } = require('@google/generative-ai');

class AIService {
    constructor() {
        this.genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
        this.model = this.genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });

        this.systemPrompt = `
You are a flight booking assistant for Cleartrip UAE. Help users search and book flights through natural conversation.

Extract flight search parameters from user messages:
- Origin and destination cities/airports
- Departure date (and return date if round trip)
- Number of passengers (adults, children, infants)
- Class preference (Economy, Business, First)
- Filters: time preference (morning, afternoon, evening), stops (nonstop, onestop), airline, price range

Always respond with JSON in this format:
{
    "intent": "search|filter|book|clarify",
    "parameters": {
        "from": "city_name",
        "to": "city_name", 
        "departDate": "DD/MM/YYYY",
        "adults": 1,
        "children": 0,
        "infants": 0,
        "class": "Economy"
    },
    "filters": {
        "timePreference": "morning|afternoon|evening",
        "stops": "nonstop|onestop|any",
        "airline": "airline_name",
        "priceRange": [min, max]
    },
    "response": "Natural language response to user",
    "missingInfo": ["list of missing required parameters"]
}

Popular cities: Delhi, Mumbai, Bangalore, Chennai, Kolkata, Hyderabad, Pune, Ahmedabad, Goa, Kochi

Current date context: ${new Date().toLocaleDateString('en-GB')}

Be helpful, friendly, and ask for clarification when needed. If the user provides incomplete information, ask for the missing details in a conversational way.
`;
    }

    async processMessage(message, conversationHistory = []) {
        try {
            // Check if Gemini API key is configured
            if (!process.env.GEMINI_API_KEY || process.env.GEMINI_API_KEY === 'your_gemini_key_here') {
                return this.getMockAIResponse(message);
            }

            // Build conversation context for Gemini
            let conversationContext = '';
            if (conversationHistory.length > 0) {
                const recentHistory = conversationHistory.slice(-10); // Keep last 10 messages
                conversationContext = recentHistory.map(msg =>
                    `${msg.role}: ${msg.content}`
                ).join('\n');
            }

            // Create the full prompt for Gemini
            const fullPrompt = `${this.systemPrompt}

Previous conversation:
${conversationContext}

Current user message: ${message}

Please respond with valid JSON only, following the exact format specified in the system prompt.`;

            const result = await this.model.generateContent(fullPrompt);
            const response = await result.response;
            const text = response.text();

            // Extract JSON from response (Gemini might include extra text)
            const jsonMatch = text.match(/\{[\s\S]*\}/);
            if (!jsonMatch) {
                throw new Error('No valid JSON found in Gemini response');
            }

            const aiResponse = JSON.parse(jsonMatch[0]);
            return this.validateAndCleanResponse(aiResponse);
        } catch (error) {
            console.error('Gemini AI processing error:', error.message);

            // Fallback to mock response if AI fails
            return this.getMockAIResponse(message);
        }
    }

    getMockAIResponse(message) {
        const lowerMessage = message.toLowerCase();
        
        // Simple pattern matching for demo purposes
        const patterns = {
            search: /(?:fly|flight|book|search|travel|go).+(?:from|to)/i,
            from: /(?:from|leaving|departing)\s+([a-z\s]+?)(?:\s+to|\s+going|\s+and|$)/i,
            to: /(?:to|going\s+to|arriving\s+in)\s+([a-z\s]+?)(?:\s+on|\s+tomorrow|\s+today|\s+and|$)/i,
            date: /(?:tomorrow|today|(\d{1,2})[\/\-](\d{1,2})[\/\-](\d{2,4})|(\d{1,2})\s+(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec))/i,
            morning: /morning|early|6am|7am|8am|9am|10am/i,
            afternoon: /afternoon|noon|12pm|1pm|2pm|3pm|4pm/i,
            evening: /evening|night|5pm|6pm|7pm|8pm|9pm|10pm/i
        };

        let intent = 'clarify';
        let parameters = {
            adults: 1,
            children: 0,
            infants: 0,
            class: 'Economy'
        };
        let filters = {};
        let missingInfo = [];
        let response = "I'd be happy to help you find flights! Could you please tell me where you'd like to fly from and to, and when you'd like to travel?";

        // Extract origin and destination using multiple patterns
        let fromCity = null;
        let toCity = null;

        // Pattern 1: "from X to Y" or "X to Y"
        const routePattern1 = lowerMessage.match(/(?:from\s+)?([a-z\s]+?)\s+to\s+([a-z\s]+?)(?:\s+on|\s+tomorrow|\s+today|\s+\d|\s*$)/i);
        if (routePattern1) {
            fromCity = this.cleanCityName(routePattern1[1]);
            toCity = this.cleanCityName(routePattern1[2]);
        }

        // Pattern 2: "from X going to Y"
        if (!fromCity || !toCity) {
            const routePattern2 = lowerMessage.match(/(?:from|leaving)\s+([a-z\s]+?)\s+(?:going\s+to|to)\s+([a-z\s]+?)(?:\s+on|\s+tomorrow|\s+today|\s+\d|\s*$)/i);
            if (routePattern2) {
                fromCity = this.cleanCityName(routePattern2[1]);
                toCity = this.cleanCityName(routePattern2[2]);
            }
        }

        // Pattern 3: Individual extraction as fallback
        if (!fromCity) {
            const fromMatch = lowerMessage.match(/(?:from|leaving)\s+([a-z\s]+?)(?:\s+to|\s+going|\s+and|\s*$)/i);
            if (fromMatch) {
                fromCity = this.cleanCityName(fromMatch[1]);
            }
        }

        if (!toCity) {
            const toMatch = lowerMessage.match(/(?:to|going\s+to|arriving\s+in)\s+([a-z\s]+?)(?:\s+on|\s+tomorrow|\s+today|\s+\d|\s*$)/i);
            if (toMatch) {
                toCity = this.cleanCityName(toMatch[1]);
            }
        }

        if (fromCity) parameters.from = fromCity;
        if (toCity) parameters.to = toCity;

        // Extract date with multiple patterns
        if (lowerMessage.includes('tomorrow')) {
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            parameters.departDate = tomorrow.toLocaleDateString('en-GB');
        } else if (lowerMessage.includes('today')) {
            parameters.departDate = new Date().toLocaleDateString('en-GB');
        } else {
            // Pattern for "15 aug", "15 august", "15/08", etc.
            const datePatterns = [
                /(\d{1,2})\s+(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,
                /(\d{1,2})\s+(january|february|march|april|may|june|july|august|september|october|november|december)/i,
                /(\d{1,2})[\/\-](\d{1,2})[\/\-](\d{2,4})/,
                /(\d{1,2})[\/\-](\d{1,2})/
            ];

            for (const pattern of datePatterns) {
                const match = lowerMessage.match(pattern);
                if (match) {
                    if (pattern.source.includes('jan|feb')) {
                        // Month name pattern
                        const day = parseInt(match[1]);
                        const monthName = match[2].toLowerCase();
                        const monthMap = {
                            'jan': 0, 'january': 0, 'feb': 1, 'february': 1, 'mar': 2, 'march': 2,
                            'apr': 3, 'april': 3, 'may': 4, 'jun': 5, 'june': 5,
                            'jul': 6, 'july': 6, 'aug': 7, 'august': 7, 'sep': 8, 'september': 8,
                            'oct': 9, 'october': 9, 'nov': 10, 'november': 10, 'dec': 11, 'december': 11
                        };

                        if (monthMap.hasOwnProperty(monthName)) {
                            const currentYear = new Date().getFullYear();
                            const date = new Date(currentYear, monthMap[monthName], day);

                            // If the date is in the past, assume next year
                            if (date < new Date()) {
                                date.setFullYear(currentYear + 1);
                            }

                            parameters.departDate = date.toLocaleDateString('en-GB');
                        }
                    } else if (match[3]) {
                        // Full date pattern DD/MM/YYYY
                        const day = match[1];
                        const month = match[2];
                        const year = match[3].length === 2 ? '20' + match[3] : match[3];
                        parameters.departDate = `${day}/${month}/${year}`;
                    } else if (match[2]) {
                        // DD/MM pattern - assume current year
                        const day = match[1];
                        const month = match[2];
                        const year = new Date().getFullYear();
                        parameters.departDate = `${day}/${month}/${year}`;
                    }
                    break;
                }
            }
        }

        // Extract time preference
        if (patterns.morning.test(lowerMessage)) {
            filters.timePreference = 'morning';
        } else if (patterns.afternoon.test(lowerMessage)) {
            filters.timePreference = 'afternoon';
        } else if (patterns.evening.test(lowerMessage)) {
            filters.timePreference = 'evening';
        }

        // Check if we have enough info for search
        if (parameters.from && parameters.to && parameters.departDate) {
            intent = 'search';
            response = `Great! I found flights from ${parameters.from} to ${parameters.to} on ${parameters.departDate}. Let me search for the best options for you.`;
        } else {
            // Determine what's missing
            if (!parameters.from) missingInfo.push('departure city');
            if (!parameters.to) missingInfo.push('destination city');
            if (!parameters.departDate) missingInfo.push('travel date');

            if (missingInfo.length > 0) {
                response = `I need a bit more information. Could you please tell me your ${missingInfo.join(' and ')}?`;
            }
        }

        return {
            intent,
            parameters,
            filters,
            response,
            missingInfo
        };
    }

    cleanCityName(cityName) {
        return cityName.trim()
            .split(' ')
            .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
            .join(' ');
    }

    validateAndCleanResponse(response) {
        // Ensure required fields exist
        const defaultResponse = {
            intent: 'clarify',
            parameters: {
                adults: 1,
                children: 0,
                infants: 0,
                class: 'Economy'
            },
            filters: {},
            response: "I'd be happy to help you find flights!",
            missingInfo: []
        };

        return {
            ...defaultResponse,
            ...response,
            parameters: {
                ...defaultResponse.parameters,
                ...response.parameters
            },
            filters: {
                ...defaultResponse.filters,
                ...response.filters
            }
        };
    }
}

module.exports = new AIService();
