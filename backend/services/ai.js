const { GoogleGenerativeAI } = require('@google/generative-ai');

class AIService {
    constructor() {
        this.genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
        this.model = this.genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });

        this.systemPrompt = `
You are a flight booking assistant for Cleartrip UAE. Help users search and book flights through natural conversation.

Extract flight search parameters from user messages:
- Origin and destination cities/airports
- Departure date (and return date if round trip)
- Number of passengers (adults, children, infants)
- Class preference (Economy, Business, First)
- Filters: time preference (morning, afternoon, evening), stops (nonstop, onestop), airline, price range

Always respond with JSON in this format:
{
    "intent": "search|filter|book|clarify",
    "parameters": {
        "from": "city_name",
        "to": "city_name", 
        "departDate": "DD/MM/YYYY",
        "adults": 1,
        "children": 0,
        "infants": 0,
        "class": "Economy"
    },
    "filters": {
        "timePreference": "morning|afternoon|evening",
        "stops": "nonstop|onestop|any",
        "airline": "airline_name",
        "priceRange": [min, max]
    },
    "response": "Natural language response to user",
    "missingInfo": ["list of missing required parameters"]
}

Popular cities: Delhi, Mumbai, Bangalore, Chennai, Kolkata, Hyderabad, Pune, Ahmedabad, Goa, Kochi

Current date context: ${new Date().toLocaleDateString('en-GB')}

Be helpful, friendly, and ask for clarification when needed. If the user provides incomplete information, ask for the missing details in a conversational way.
`;
    }

    async processMessage(message, conversationHistory = []) {
        // Build conversation context for Gemini
        let conversationContext = '';
        if (conversationHistory.length > 0) {
            const recentHistory = conversationHistory.slice(-10); // Keep last 10 messages
            conversationContext = recentHistory.map(msg =>
                `${msg.role}: ${msg.content}`
            ).join('\n');
        }

        // Create the full prompt for Gemini
        const fullPrompt = `${this.systemPrompt}

Previous conversation:
${conversationContext}

Current user message: ${message}

Please respond with valid JSON only, following the exact format specified in the system prompt.`;

        const result = await this.model.generateContent(fullPrompt);
        const response = await result.response;
        const text = response.text();

        // Extract JSON from response (Gemini might include extra text)
        const jsonMatch = text.match(/\{[\s\S]*\}/);
        if (!jsonMatch) {
            throw new Error('No valid JSON found in Gemini response');
        }

        const aiResponse = JSON.parse(jsonMatch[0]);
        return this.validateAndCleanResponse(aiResponse);
    }

    validateAndCleanResponse(response) {
        // Ensure required fields exist
        const defaultResponse = {
            intent: 'clarify',
            parameters: {
                adults: 1,
                children: 0,
                infants: 0,
                class: 'Economy'
            },
            filters: {},
            response: "I'd be happy to help you find flights!",
            missingInfo: []
        };

        return {
            ...defaultResponse,
            ...response,
            parameters: {
                ...defaultResponse.parameters,
                ...response.parameters
            },
            filters: {
                ...defaultResponse.filters,
                ...response.filters
            }
        };
    }
}

module.exports = new AIService();
