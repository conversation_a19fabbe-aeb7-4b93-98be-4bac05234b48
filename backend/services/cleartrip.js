const axios = require('axios');

class CleartripService {
    constructor() {
        this.baseUrl = process.env.CLEARTRIP_BASE_URL || 'https://me.cleartrip.ae';
        this.defaultHeaders = {
            'accept': 'application/json',
            'content-type': 'application/json',
            'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        };
        
        // Airport code mapping for popular Indian cities
        this.airportCodes = {
            'delhi': 'DEL',
            'mumbai': 'BOM',
            'bangalore': 'BLR',
            'chennai': 'MAA',
            'kolkata': 'CCU',
            'hyderabad': 'HYD',
            'pune': 'PNQ',
            'ahmedabad': 'AMD',
            'goa': 'GOI',
            'kochi': 'COK',
            'jaipur': 'JAI',
            'lucknow': 'LKO',
            'chandigarh': 'IXC',
            'bhubaneswar': 'BBI',
            'indore': 'IDR'
        };
    }

    async searchFlights(params) {
        try {
            console.log('Searching flights with params:', params);
            
            // For demo purposes, return mock data since we don't have actual API access.
            //return this.getMockFlightData(params);
            
            // Uncomment below for actual API integration
        
            const searchUrl = this.buildSearchUrl(params);
            const response = await axios.get(searchUrl, {
                headers: this.defaultHeaders,
                timeout: 10000
            });
            return this.parseFlightResults(response.data);
            
        } catch (error) {
            console.error('Flight search error:', error.message);
            throw new Error(`Flight search failed: ${error.message}`);
        }
    }

    buildSearchUrl(params) {
        const {
            from, to, departDate, adults = 1, 
            children = 0, infants = 0, class: flightClass = 'Economy'
        } = params;
        
        const fromCode = this.getAirportCode(from);
        const toCode = this.getAirportCode(to);
        
        const queryParams = new URLSearchParams({
            adults,
            childs: children,
            infants,
            class: flightClass,
            depart_date: departDate,
            from: fromCode,
            to: toCode,
            intl: 'n',
            origin: `${fromCode} - ${from}`,
            destination: `${toCode} - ${to}`,
            sd: Date.now()
        });

        return `${this.baseUrl}/node/flight/search?${queryParams}`;
    }

    getAirportCode(cityName) {
        const city = cityName.toLowerCase().trim();
        return this.airportCodes[city] || city.toUpperCase();
    }

    getMockFlightData(params) {
        const { from, to, departDate } = params;
        const fromCode = this.getAirportCode(from);
        const toCode = this.getAirportCode(to);
        
        // Generate mock flight data
        const airlines = ['IndiGo', 'Air India', 'SpiceJet', 'Vistara', 'GoAir'];
        const flights = [];
        
        for (let i = 0; i < 5; i++) {
            const airline = airlines[i % airlines.length];
            const basePrice = 300 + Math.random() * 500; // AED prices (roughly 1/10 of INR)
            const departureHour = 6 + (i * 3);
            const duration = 2 + Math.random() * 3;
            
            flights.push({
                id: `FL${Date.now()}_${i}`,
                airline: airline,
                departure: {
                    time: `${departureHour.toString().padStart(2, '0')}:${(Math.random() * 60).toFixed(0).padStart(2, '0')}`,
                    airport: fromCode,
                    city: from
                },
                arrival: {
                    time: `${(departureHour + duration).toFixed(0).padStart(2, '0')}:${(Math.random() * 60).toFixed(0).padStart(2, '0')}`,
                    airport: toCode,
                    city: to
                },
                duration: `${duration.toFixed(0)}h ${(Math.random() * 60).toFixed(0)}m`,
                price: Math.round(basePrice),
                currency: 'AED',
                stops: Math.random() > 0.7 ? 1 : 0,
                fareType: Math.random() > 0.5 ? 'Refundable' : 'Non-refundable',
                date: departDate
            });
        }
        
        return flights.sort((a, b) => a.price - b.price);
    }

    parseFlightResults(data) {
        // Parse actual Cleartrip API response
        if (!data || !data.flights) {
            return [];
        }
        
        return data.flights.map(flight => ({
            id: flight.id || `FL${Date.now()}_${Math.random()}`,
            airline: flight.airline?.name || 'Unknown Airline',
            departure: {
                time: flight.departure?.time || '00:00',
                airport: flight.departure?.airport || 'N/A',
                city: flight.departure?.city || 'N/A'
            },
            arrival: {
                time: flight.arrival?.time || '00:00',
                airport: flight.arrival?.airport || 'N/A',
                city: flight.arrival?.city || 'N/A'
            },
            duration: flight.duration || 'N/A',
            price: flight.price?.amount || 0,
            currency: flight.price?.currency || 'AED',
            stops: flight.stops || 0,
            fareType: flight.fareType || 'Economy'
        }));
    }

    async createBookingItinerary(flightData) {
        try {
            // For demo purposes, return a mock booking URL
            const bookingId = `BK${Date.now()}`;
            return {
                bookingUrl: `${this.baseUrl}/booking/${bookingId}`,
                bookingId: bookingId,
                status: 'pending'
            };
            
            // Uncomment for actual booking implementation
            /*
            const response = await axios.post(`${this.baseUrl}/itin/itinerary/create`, {
                flightParams: [flightData],
                // Add other required parameters
            }, {
                headers: {
                    ...this.defaultHeaders,
                    // Add authentication headers
                }
            });
            return response.data;
            */
        } catch (error) {
            console.error('Booking creation error:', error.message);
            throw new Error(`Booking creation failed: ${error.message}`);
        }
    }
}

module.exports = new CleartripService();
