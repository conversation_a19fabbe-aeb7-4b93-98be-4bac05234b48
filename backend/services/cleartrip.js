const axios = require('axios');

class CleartripService {
    constructor() {
        this.baseUrl = process.env.CLEARTRIP_BASE_URL || 'https://me.cleartrip.ae';
        this.useRealAPI = process.env.USE_REAL_CLEARTRIP_API === 'true';
        this.defaultHeaders = {
            'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'accept-language': 'en-US,en;q=0.9',
            'accept-encoding': 'gzip, deflate, br',
            'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'sec-ch-ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"macOS"',
            'sec-fetch-dest': 'document',
            'sec-fetch-mode': 'navigate',
            'sec-fetch-site': 'none',
            'sec-fetch-user': '?1',
            'upgrade-insecure-requests': '1'
        };

        // Log initialization
        console.log('🚀 [CLEARTRIP SERVICE] Initializing Cleartrip Service');
        console.log('🌐 [CLEARTRIP SERVICE] Base URL:', this.baseUrl);
        console.log('🔧 [CLEARTRIP SERVICE] Real API Mode:', this.useRealAPI ? 'ENABLED' : 'DISABLED (Mock Mode)');
        console.log('📋 [CLEARTRIP SERVICE] Default Headers:', JSON.stringify(this.defaultHeaders, null, 2));
        
        // Airport code mapping for popular cities in UAE and region
        this.airportCodes = {
            'delhi': 'DEL',
            'mumbai': 'BOM',
            'bangalore': 'BLR',
            'chennai': 'MAA',
            'kolkata': 'CCU',
            'hyderabad': 'HYD',
            'pune': 'PNQ',
            'ahmedabad': 'AMD',
            'goa': 'GOI',
            'kochi': 'COK',
            'jaipur': 'JAI',
            'lucknow': 'LKO',
            'chandigarh': 'IXC',
            'bhubaneswar': 'BBI',
            'indore': 'IDR',
            // UAE and Middle East airports
            'dubai': 'DXB',
            'abu dhabi': 'AUH',
            'sharjah': 'SHJ',
            'muscat': 'MCT',
            'doha': 'DOH',
            'kuwait': 'KWI',
            'riyadh': 'RUH',
            'jeddah': 'JED',
            'bahrain': 'BAH',
            'ras al khaimah': 'RKT'
        };
    }

    async searchFlights(params) {
        try {
            console.log('🔍 [CLEARTRIP API] Starting flight search with params:', JSON.stringify(params, null, 2));
            console.log('� [CLEARTRIP API] Using REAL API - no mock data');

            // Build the search URL
            const searchUrl = this.buildSearchUrl(params);
            console.log('🌐 [CLEARTRIP API] Request URL:', searchUrl);
            console.log('📋 [CLEARTRIP API] Request Headers:', JSON.stringify(this.defaultHeaders, null, 2));

            // Log the actual API call attempt
            const startTime = Date.now();
            console.log(`⏰ [CLEARTRIP API] Making REAL API call to ${this.baseUrl} at ${new Date().toISOString()}`);

            try {
                const response = await axios.get(searchUrl, {
                    headers: this.defaultHeaders,
                    timeout: 15000
                });

                const endTime = Date.now();
                const duration = endTime - startTime;

                console.log(`✅ [CLEARTRIP API] Response received in ${duration}ms`);
                console.log('📊 [CLEARTRIP API] Response Status:', response.status);
                console.log('📋 [CLEARTRIP API] Response Headers:', JSON.stringify(response.headers, null, 2));
                console.log('📦 [CLEARTRIP API] Response Data Size:', JSON.stringify(response.data).length, 'characters');

                // Log first 500 characters of response for debugging
                const responsePreview = JSON.stringify(response.data).substring(0, 500);
                console.log('👀 [CLEARTRIP API] Response Preview:', responsePreview + '...');

                const parsedFlights = this.parseFlightResults(response.data);
                console.log(`🛫 [CLEARTRIP API] Parsed ${parsedFlights.length} flights from response`);

                return parsedFlights;

            } catch (apiError) {
                const endTime = Date.now();
                const duration = endTime - startTime;

                console.error(`❌ [CLEARTRIP API] API call failed after ${duration}ms`);
                console.error('🚨 [CLEARTRIP API] Error Details:', {
                    message: apiError.message,
                    status: apiError.response?.status,
                    statusText: apiError.response?.statusText,
                    headers: apiError.response?.headers,
                    data: apiError.response?.data
                });

                // Return empty array instead of mock data
                console.log('🔄 [CLEARTRIP API] Returning empty results due to API error');
                return [];
            }

        } catch (error) {
            console.error('💥 [CLEARTRIP API] Unexpected error in searchFlights:', error.message);
            console.error('📍 [CLEARTRIP API] Error Stack:', error.stack);

            // Return empty array instead of mock data
            console.log('🔄 [CLEARTRIP API] Returning empty results due to unexpected error');
            return [];
        }
    }

    buildSearchUrl(params) {
        console.log('🔧 [CLEARTRIP URL] Building search URL with params:', JSON.stringify(params, null, 2));

        const {
            from, to, departDate, adults = 1,
            children = 0, infants = 0, class: flightClass = 'Economy'
        } = params;

        const fromCode = this.getAirportCode(from);
        const toCode = this.getAirportCode(to);

        console.log('🏢 [CLEARTRIP URL] Airport code mapping:', {
            from: `${from} → ${fromCode}`,
            to: `${to} → ${toCode}`
        });

        // Determine if this is international or domestic flight
        const isInternational = this.isInternationalFlight(fromCode, toCode);
        const flightType = isInternational ? 'international' : 'domestic';

        console.log('✈️ [CLEARTRIP URL] Flight type:', isInternational ? 'International' : 'Domestic');

        const queryParams = new URLSearchParams({
            adults,
            childs: children,
            infants,
            class: flightClass,
            depart_date: departDate,
            from: fromCode,
            to: toCode,
            intl: isInternational ? 'y' : 'n',
            origin: `${fromCode} - ${from}`,
            destination: `${toCode} - ${to}`,
            sd: Date.now(),
            rnd_one: 'O'
        });

        const fullUrl = `${this.baseUrl}/flights/${flightType}/results?${queryParams}`;
        console.log('🔗 [CLEARTRIP URL] Generated search URL:', fullUrl);
        console.log('📋 [CLEARTRIP URL] Query parameters:', Object.fromEntries(queryParams));

        return fullUrl;
    }

    getAirportCode(cityName) {
        const city = cityName.toLowerCase().trim();
        return this.airportCodes[city] || city.toUpperCase();
    }

    isInternationalFlight(fromCode, toCode) {
        const fromCountry = this.getCountryByAirport(fromCode);
        const toCountry = this.getCountryByAirport(toCode);

        console.log('🌍 [CLEARTRIP URL] Country mapping:', {
            from: `${fromCode} → ${fromCountry}`,
            to: `${toCode} → ${toCountry}`
        });

        return fromCountry !== toCountry;
    }

    getCountryByAirport(airportCode) {
        const uaeAirports = ['DXB', 'AUH', 'SHJ', 'RKT'];
        const indianAirports = ['DEL', 'BOM', 'BLR', 'MAA', 'CCU', 'HYD', 'PNQ', 'AMD', 'GOI', 'COK', 'JAI', 'LKO', 'IXC', 'BBI', 'IDR'];
        const omanAirports = ['MCT'];
        const qatarAirports = ['DOH'];
        const kuwaitAirports = ['KWI'];
        const saudiAirports = ['RUH', 'JED'];
        const bahrainAirports = ['BAH'];

        if (uaeAirports.includes(airportCode)) return 'UAE';
        if (indianAirports.includes(airportCode)) return 'India';
        if (omanAirports.includes(airportCode)) return 'Oman';
        if (qatarAirports.includes(airportCode)) return 'Qatar';
        if (kuwaitAirports.includes(airportCode)) return 'Kuwait';
        if (saudiAirports.includes(airportCode)) return 'Saudi Arabia';
        if (bahrainAirports.includes(airportCode)) return 'Bahrain';

        return 'Unknown';
    }

    getMockFlightData(params) {
        console.log('🎭 [CLEARTRIP MOCK] Generating mock flight data for params:', JSON.stringify(params, null, 2));

        const { from, to, departDate } = params;
        const fromCode = this.getAirportCode(from);
        const toCode = this.getAirportCode(to);

        console.log('🏢 [CLEARTRIP MOCK] Using airport codes:', { fromCode, toCode });

        // Generate mock flight data
        const airlines = ['IndiGo', 'Air India', 'SpiceJet', 'Vistara', 'GoAir'];
        const flights = [];

        console.log('✈️ [CLEARTRIP MOCK] Generating flights for airlines:', airlines);

        for (let i = 0; i < 5; i++) {
            const airline = airlines[i % airlines.length];
            const basePrice = 300 + Math.random() * 500; // AED prices (roughly 1/10 of INR)
            const departureHour = 6 + (i * 3);
            const duration = 2 + Math.random() * 3;

            const flight = {
                id: `FL${Date.now()}_${i}`,
                airline: airline,
                departure: {
                    time: `${departureHour.toString().padStart(2, '0')}:${(Math.random() * 60).toFixed(0).padStart(2, '0')}`,
                    airport: fromCode,
                    city: from
                },
                arrival: {
                    time: `${(departureHour + duration).toFixed(0).padStart(2, '0')}:${(Math.random() * 60).toFixed(0).padStart(2, '0')}`,
                    airport: toCode,
                    city: to
                },
                duration: `${duration.toFixed(0)}h ${(Math.random() * 60).toFixed(0)}m`,
                price: Math.round(basePrice),
                currency: 'AED',
                stops: Math.random() > 0.7 ? 1 : 0,
                fareType: Math.random() > 0.5 ? 'Refundable' : 'Non-refundable',
                date: departDate
            };

            flights.push(flight);
            console.log(`🛫 [CLEARTRIP MOCK] Generated flight ${i + 1}:`, {
                airline: flight.airline,
                route: `${flight.departure.time} ${fromCode} → ${flight.arrival.time} ${toCode}`,
                price: `${flight.price} ${flight.currency}`,
                duration: flight.duration,
                stops: flight.stops
            });
        }

        const sortedFlights = flights.sort((a, b) => a.price - b.price);
        console.log(`🎯 [CLEARTRIP MOCK] Generated ${sortedFlights.length} mock flights, sorted by price`);
        console.log('💰 [CLEARTRIP MOCK] Price range:', {
            cheapest: `${sortedFlights[0].price} AED`,
            mostExpensive: `${sortedFlights[sortedFlights.length - 1].price} AED`
        });

        return sortedFlights;
    }

    parseFlightResults(data) {
        console.log('🔍 [CLEARTRIP PARSER] Starting to parse flight results');
        console.log('📦 [CLEARTRIP PARSER] Raw data structure:', {
            hasData: !!data,
            dataType: typeof data,
            hasCards: !!(data && data.cards),
            cardCount: data?.cards?.length || 0
        });

        // Parse actual Cleartrip API response - the response has a 'cards' structure
        if (!data || !data.cards || !Array.isArray(data.cards)) {
            console.log('⚠️ [CLEARTRIP PARSER] No flight cards found in response');
            return [];
        }

        console.log(`✈️ [CLEARTRIP PARSER] Processing ${data.cards.length} flight card groups from API response`);

        const parsedFlights = [];

        // Iterate through card groups
        data.cards.forEach((cardGroup, groupIndex) => {
            if (Array.isArray(cardGroup)) {
                cardGroup.forEach((card, cardIndex) => {
                    try {
                        const parsedFlight = this.parseFlightCard(card, `${groupIndex}_${cardIndex}`);
                        if (parsedFlight) {
                            parsedFlights.push(parsedFlight);
                            console.log(`🛫 [CLEARTRIP PARSER] Parsed flight ${parsedFlights.length}:`, {
                                id: parsedFlight.id,
                                airline: parsedFlight.airline,
                                route: `${parsedFlight.departure.time} ${parsedFlight.departure.airport} → ${parsedFlight.arrival.time} ${parsedFlight.arrival.airport}`,
                                price: `${parsedFlight.price} ${parsedFlight.currency}`,
                                duration: parsedFlight.duration,
                                stops: parsedFlight.stops
                            });
                        }
                    } catch (parseError) {
                        console.error(`❌ [CLEARTRIP PARSER] Error parsing card ${groupIndex}_${cardIndex}:`, parseError.message);
                    }
                });
            }
        });

        console.log(`✅ [CLEARTRIP PARSER] Successfully parsed ${parsedFlights.length} flights`);
        return parsedFlights;
    }

    parseFlightCard(card, cardId) {
        console.log(`🔍 [CLEARTRIP PARSER] Parsing flight card ${cardId}:`, JSON.stringify(card, null, 2).substring(0, 500) + '...');

        if (!card || !card.id) {
            console.log(`⚠️ [CLEARTRIP PARSER] Invalid card structure for ${cardId}`);
            return null;
        }

        try {
            // Extract basic flight information
            const flightId = card.id || `FL${Date.now()}_${cardId}`;
            const sectorKeys = card.sectorKeys || [];
            const airlineCodes = card.airlineCodes || [];
            const totalDuration = card.totalDurationInMinutes || 0;
            const maxStops = card.maxStopsInSectors || 0;

            // Parse sector information for departure/arrival details
            let departureInfo = { time: '00:00', airport: 'N/A', city: 'N/A' };
            let arrivalInfo = { time: '00:00', airport: 'N/A', city: 'N/A' };

            if (sectorKeys.length > 0) {
                const sectorKey = sectorKeys[0]; // Take first sector
                const sectorParts = sectorKey.split('_');
                if (sectorParts.length >= 4) {
                    departureInfo.airport = sectorParts[0];
                    arrivalInfo.airport = sectorParts[1];

                    // Parse date and time from sector key
                    const dateTime = sectorParts[2];
                    if (dateTime && dateTime.length >= 10) {
                        const timeStr = dateTime.substring(8, 12);
                        if (timeStr.length === 4) {
                            departureInfo.time = `${timeStr.substring(0, 2)}:${timeStr.substring(2, 4)}`;
                        }
                    }
                }
            }

            // Format duration
            const hours = Math.floor(totalDuration / 60);
            const minutes = totalDuration % 60;
            const durationStr = `${hours}h ${minutes}m`;

            // Get airline name
            const airlineName = airlineCodes.length > 0 ? airlineCodes[0] : 'Unknown';

            const parsedFlight = {
                id: flightId,
                airline: airlineName,
                departure: departureInfo,
                arrival: arrivalInfo,
                duration: durationStr,
                price: 0, // Price will be extracted separately if available
                currency: 'AED',
                stops: maxStops,
                fareType: 'Economy'
            };

            console.log(`✅ [CLEARTRIP PARSER] Successfully parsed card ${cardId}`);
            return parsedFlight;

        } catch (error) {
            console.error(`❌ [CLEARTRIP PARSER] Error parsing flight card ${cardId}:`, error.message);
            return null;
        }
    }

    async createBookingItinerary(flightData) {
        try {
            console.log('🎫 [CLEARTRIP BOOKING] Starting booking process with flight data:', JSON.stringify(flightData, null, 2));

            // For demo purposes, return a mock booking URL
            const bookingId = `BK${Date.now()}`;
            const mockBookingResult = {
                bookingUrl: `${this.baseUrl}/booking/${bookingId}`,
                bookingId: bookingId,
                status: 'pending'
            };

            console.log('🔄 [CLEARTRIP BOOKING] Using mock booking (demo mode)');
            console.log('📋 [CLEARTRIP BOOKING] Mock booking result:', JSON.stringify(mockBookingResult, null, 2));
            console.log('✅ [CLEARTRIP BOOKING] Returning booking result to caller');

            return mockBookingResult;

            // // Uncomment for actual booking implementation
            // /*
            // console.log('🌐 [CLEARTRIP BOOKING] Making actual booking API call to:', `${this.baseUrl}/itin/itinerary/create`);

            // const bookingPayload = {
            //     flightParams: [flightData],
            //     // Add other required parameters
            // };

            // console.log('📦 [CLEARTRIP BOOKING] Booking payload:', JSON.stringify(bookingPayload, null, 2));
            // console.log('📋 [CLEARTRIP BOOKING] Booking headers:', JSON.stringify({
            //     ...this.defaultHeaders,
            //     // Add authentication headers
            // }, null, 2));

            // const startTime = Date.now();
            // console.log(`⏰ [CLEARTRIP BOOKING] Making booking API call at ${new Date().toISOString()}`);

            // const response = await axios.post(`${this.baseUrl}/itin/itinerary/create`, bookingPayload, {
            //     headers: {
            //         ...this.defaultHeaders,
            //         // Add authentication headers
            //     }
            // });

            // const endTime = Date.now();
            // const duration = endTime - startTime;

            // console.log(`✅ [CLEARTRIP BOOKING] Booking response received in ${duration}ms`);
            // console.log('📊 [CLEARTRIP BOOKING] Response Status:', response.status);
            // console.log('📋 [CLEARTRIP BOOKING] Response Headers:', JSON.stringify(response.headers, null, 2));
            // console.log('📦 [CLEARTRIP BOOKING] Booking Response:', JSON.stringify(response.data, null, 2));

            // return response.data;
            // */
        } catch (error) {
            console.error('💥 [CLEARTRIP BOOKING] Booking creation error:', error.message);
            console.error('📍 [CLEARTRIP BOOKING] Error Stack:', error.stack);

            if (error.response) {
                console.error('🚨 [CLEARTRIP BOOKING] API Error Response:', {
                    status: error.response.status,
                    statusText: error.response.statusText,
                    headers: error.response.headers,
                    data: error.response.data
                });
            }

            throw new Error(`Booking creation failed: ${error.message}`);
        }
    }
}

module.exports = new CleartripService();
