const axios = require('axios');

class CleartripService {
    constructor() {
        this.baseUrl = process.env.CLEARTRIP_BASE_URL || 'https://me.cleartrip.ae';
        this.useRealAPI = process.env.USE_REAL_CLEARTRIP_API === 'true';
        this.defaultHeaders = {
            'accept': 'application/json',
            'content-type': 'application/json',
            'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        };

        // Log initialization
        console.log('🚀 [CLEARTRIP SERVICE] Initializing Cleartrip Service');
        console.log('🌐 [CLEARTRIP SERVICE] Base URL:', this.baseUrl);
        console.log('🔧 [CLEARTRIP SERVICE] Real API Mode:', this.useRealAPI ? 'ENABLED' : 'DISABLED (Mock Mode)');
        console.log('📋 [CLEARTRIP SERVICE] Default Headers:', JSON.stringify(this.defaultHeaders, null, 2));
        
        // Airport code mapping for popular Indian cities
        this.airportCodes = {
            'delhi': 'DEL',
            'mumbai': 'BOM',
            'bangalore': 'BLR',
            'chennai': 'MAA',
            'kolkata': 'CCU',
            'hyderabad': 'HYD',
            'pune': 'PNQ',
            'ahmedabad': 'AMD',
            'goa': 'GOI',
            'kochi': 'COK',
            'jaipur': 'JAI',
            'lucknow': 'LKO',
            'chandigarh': 'IXC',
            'bhubaneswar': 'BBI',
            'indore': 'IDR'
        };
    }

    async searchFlights(params) {
        try {
            console.log('🔍 [CLEARTRIP API] Starting flight search with params:', JSON.stringify(params, null, 2));

            // Check if we should use real API or mock data
            if (!this.useRealAPI) {
                console.log('🎭 [CLEARTRIP API] Real API disabled, using mock data');
                return this.getMockFlightData(params);
            }

            // Build the search URL
            const searchUrl = this.buildSearchUrl(params);
            console.log('🌐 [CLEARTRIP API] Request URL:', searchUrl);
            console.log('📋 [CLEARTRIP API] Request Headers:', JSON.stringify(this.defaultHeaders, null, 2));

            // Log the actual API call attempt
            const startTime = Date.now();
            console.log(`⏰ [CLEARTRIP API] Making REAL API call to ${this.baseUrl} at ${new Date().toISOString()}`);

            try {
                const response = await axios.get(searchUrl, {
                    headers: this.defaultHeaders,
                    timeout: 15000
                });

                const endTime = Date.now();
                const duration = endTime - startTime;

                console.log(`✅ [CLEARTRIP API] Response received in ${duration}ms`);
                console.log('📊 [CLEARTRIP API] Response Status:', response.status);
                console.log('📋 [CLEARTRIP API] Response Headers:', JSON.stringify(response.headers, null, 2));
                console.log('📦 [CLEARTRIP API] Response Data Size:', JSON.stringify(response.data).length, 'characters');

                // Log first 500 characters of response for debugging
                const responsePreview = JSON.stringify(response.data).substring(0, 500);
                console.log('👀 [CLEARTRIP API] Response Preview:', responsePreview + '...');

                const parsedFlights = this.parseFlightResults(response.data);
                console.log(`🛫 [CLEARTRIP API] Parsed ${parsedFlights.length} flights from response`);

                return parsedFlights;

            } catch (apiError) {
                const endTime = Date.now();
                const duration = endTime - startTime;

                console.error(`❌ [CLEARTRIP API] API call failed after ${duration}ms`);
                console.error('🚨 [CLEARTRIP API] Error Details:', {
                    message: apiError.message,
                    status: apiError.response?.status,
                    statusText: apiError.response?.statusText,
                    headers: apiError.response?.headers,
                    data: apiError.response?.data
                });

                // Fallback to mock data with logging
                console.log('🔄 [CLEARTRIP API] Falling back to mock data due to API error');
                return this.getMockFlightData(params);
            }

        } catch (error) {
            console.error('💥 [CLEARTRIP API] Unexpected error in searchFlights:', error.message);
            console.error('📍 [CLEARTRIP API] Error Stack:', error.stack);

            // Fallback to mock data
            console.log('🔄 [CLEARTRIP API] Using mock data due to unexpected error');
            return this.getMockFlightData(params);
        }
    }

    buildSearchUrl(params) {
        console.log('🔧 [CLEARTRIP URL] Building search URL with params:', JSON.stringify(params, null, 2));

        const {
            from, to, departDate, adults = 1,
            children = 0, infants = 0, class: flightClass = 'Economy'
        } = params;

        const fromCode = this.getAirportCode(from);
        const toCode = this.getAirportCode(to);

        console.log('🏢 [CLEARTRIP URL] Airport code mapping:', {
            from: `${from} → ${fromCode}`,
            to: `${to} → ${toCode}`
        });

        const queryParams = new URLSearchParams({
            adults,
            childs: children,
            infants,
            class: flightClass,
            depart_date: departDate,
            from: fromCode,
            to: toCode,
            intl: 'n',
            origin: `${fromCode} - ${from}`,
            destination: `${toCode} - ${to}`,
            sd: Date.now()
        });

        const fullUrl = `${this.baseUrl}/node/flight/search?${queryParams}`;
        console.log('🔗 [CLEARTRIP URL] Generated search URL:', fullUrl);
        console.log('📋 [CLEARTRIP URL] Query parameters:', Object.fromEntries(queryParams));

        return fullUrl;
    }

    getAirportCode(cityName) {
        const city = cityName.toLowerCase().trim();
        return this.airportCodes[city] || city.toUpperCase();
    }

    getMockFlightData(params) {
        console.log('🎭 [CLEARTRIP MOCK] Generating mock flight data for params:', JSON.stringify(params, null, 2));

        const { from, to, departDate } = params;
        const fromCode = this.getAirportCode(from);
        const toCode = this.getAirportCode(to);

        console.log('🏢 [CLEARTRIP MOCK] Using airport codes:', { fromCode, toCode });

        // Generate mock flight data
        const airlines = ['IndiGo', 'Air India', 'SpiceJet', 'Vistara', 'GoAir'];
        const flights = [];

        console.log('✈️ [CLEARTRIP MOCK] Generating flights for airlines:', airlines);

        for (let i = 0; i < 5; i++) {
            const airline = airlines[i % airlines.length];
            const basePrice = 300 + Math.random() * 500; // AED prices (roughly 1/10 of INR)
            const departureHour = 6 + (i * 3);
            const duration = 2 + Math.random() * 3;

            const flight = {
                id: `FL${Date.now()}_${i}`,
                airline: airline,
                departure: {
                    time: `${departureHour.toString().padStart(2, '0')}:${(Math.random() * 60).toFixed(0).padStart(2, '0')}`,
                    airport: fromCode,
                    city: from
                },
                arrival: {
                    time: `${(departureHour + duration).toFixed(0).padStart(2, '0')}:${(Math.random() * 60).toFixed(0).padStart(2, '0')}`,
                    airport: toCode,
                    city: to
                },
                duration: `${duration.toFixed(0)}h ${(Math.random() * 60).toFixed(0)}m`,
                price: Math.round(basePrice),
                currency: 'AED',
                stops: Math.random() > 0.7 ? 1 : 0,
                fareType: Math.random() > 0.5 ? 'Refundable' : 'Non-refundable',
                date: departDate
            };

            flights.push(flight);
            console.log(`🛫 [CLEARTRIP MOCK] Generated flight ${i + 1}:`, {
                airline: flight.airline,
                route: `${flight.departure.time} ${fromCode} → ${flight.arrival.time} ${toCode}`,
                price: `${flight.price} ${flight.currency}`,
                duration: flight.duration,
                stops: flight.stops
            });
        }

        const sortedFlights = flights.sort((a, b) => a.price - b.price);
        console.log(`🎯 [CLEARTRIP MOCK] Generated ${sortedFlights.length} mock flights, sorted by price`);
        console.log('💰 [CLEARTRIP MOCK] Price range:', {
            cheapest: `${sortedFlights[0].price} AED`,
            mostExpensive: `${sortedFlights[sortedFlights.length - 1].price} AED`
        });

        return sortedFlights;
    }

    parseFlightResults(data) {
        console.log('🔍 [CLEARTRIP PARSER] Starting to parse flight results');
        console.log('📦 [CLEARTRIP PARSER] Raw data structure:', {
            hasData: !!data,
            dataType: typeof data,
            hasFlights: !!(data && data.flights),
            flightCount: data?.flights?.length || 0
        });

        // Parse actual Cleartrip API response
        if (!data || !data.flights) {
            console.log('⚠️ [CLEARTRIP PARSER] No flight data found in response');
            return [];
        }

        console.log(`✈️ [CLEARTRIP PARSER] Processing ${data.flights.length} flights from API response`);

        const parsedFlights = data.flights.map((flight, index) => {
            const parsedFlight = {
                id: flight.id || `FL${Date.now()}_${Math.random()}`,
                airline: flight.airline?.name || 'Unknown Airline',
                departure: {
                    time: flight.departure?.time || '00:00',
                    airport: flight.departure?.airport || 'N/A',
                    city: flight.departure?.city || 'N/A'
                },
                arrival: {
                    time: flight.arrival?.time || '00:00',
                    airport: flight.arrival?.airport || 'N/A',
                    city: flight.arrival?.city || 'N/A'
                },
                duration: flight.duration || 'N/A',
                price: flight.price?.amount || 0,
                currency: flight.price?.currency || 'AED',
                stops: flight.stops || 0,
                fareType: flight.fareType || 'Economy'
            };

            console.log(`🛫 [CLEARTRIP PARSER] Parsed flight ${index + 1}:`, {
                id: parsedFlight.id,
                airline: parsedFlight.airline,
                route: `${parsedFlight.departure.time} ${parsedFlight.departure.airport} → ${parsedFlight.arrival.time} ${parsedFlight.arrival.airport}`,
                price: `${parsedFlight.price} ${parsedFlight.currency}`,
                duration: parsedFlight.duration,
                stops: parsedFlight.stops
            });

            return parsedFlight;
        });

        console.log(`✅ [CLEARTRIP PARSER] Successfully parsed ${parsedFlights.length} flights`);
        return parsedFlights;
    }

    async createBookingItinerary(flightData) {
        try {
            console.log('🎫 [CLEARTRIP BOOKING] Starting booking process with flight data:', JSON.stringify(flightData, null, 2));

            // For demo purposes, return a mock booking URL
            const bookingId = `BK${Date.now()}`;
            const mockBookingResult = {
                bookingUrl: `${this.baseUrl}/booking/${bookingId}`,
                bookingId: bookingId,
                status: 'pending'
            };

            console.log('🔄 [CLEARTRIP BOOKING] Using mock booking (demo mode)');
            console.log('📋 [CLEARTRIP BOOKING] Mock booking result:', JSON.stringify(mockBookingResult, null, 2));
            console.log('✅ [CLEARTRIP BOOKING] Returning booking result to caller');

            return mockBookingResult;

            // // Uncomment for actual booking implementation
            // /*
            // console.log('🌐 [CLEARTRIP BOOKING] Making actual booking API call to:', `${this.baseUrl}/itin/itinerary/create`);

            // const bookingPayload = {
            //     flightParams: [flightData],
            //     // Add other required parameters
            // };

            // console.log('📦 [CLEARTRIP BOOKING] Booking payload:', JSON.stringify(bookingPayload, null, 2));
            // console.log('📋 [CLEARTRIP BOOKING] Booking headers:', JSON.stringify({
            //     ...this.defaultHeaders,
            //     // Add authentication headers
            // }, null, 2));

            // const startTime = Date.now();
            // console.log(`⏰ [CLEARTRIP BOOKING] Making booking API call at ${new Date().toISOString()}`);

            // const response = await axios.post(`${this.baseUrl}/itin/itinerary/create`, bookingPayload, {
            //     headers: {
            //         ...this.defaultHeaders,
            //         // Add authentication headers
            //     }
            // });

            // const endTime = Date.now();
            // const duration = endTime - startTime;

            // console.log(`✅ [CLEARTRIP BOOKING] Booking response received in ${duration}ms`);
            // console.log('📊 [CLEARTRIP BOOKING] Response Status:', response.status);
            // console.log('📋 [CLEARTRIP BOOKING] Response Headers:', JSON.stringify(response.headers, null, 2));
            // console.log('📦 [CLEARTRIP BOOKING] Booking Response:', JSON.stringify(response.data, null, 2));

            // return response.data;
            // */
        } catch (error) {
            console.error('💥 [CLEARTRIP BOOKING] Booking creation error:', error.message);
            console.error('📍 [CLEARTRIP BOOKING] Error Stack:', error.stack);

            if (error.response) {
                console.error('🚨 [CLEARTRIP BOOKING] API Error Response:', {
                    status: error.response.status,
                    statusText: error.response.statusText,
                    headers: error.response.headers,
                    data: error.response.data
                });
            }

            throw new Error(`Booking creation failed: ${error.message}`);
        }
    }
}

module.exports = new CleartripService();
